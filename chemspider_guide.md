# ChemSpider API 使用指南 (2024年版)

## 🔍 背景信息

根据我的调研，ChemSpider 的 API 在 2024 年发生了重大变化：

1. **旧的免费 API 端点已经弃用**
2. **现在需要通过 RSC (Royal Society of Chemistry) 注册获取 API key**
3. **KNIME 等工具的 ChemSpider 节点已被标记为弃用**

## 📋 获取 API Key 的步骤

### 1. 注册 RSC 开发者账户
- 访问：https://developer.rsc.org/
- 点击注册或登录
- 填写必要信息创建账户

### 2. 获取 API Key
- 登录后在开发者门户中找到 API key 管理
- 生成新的 API key
- 保存好您的 API key（通常是一长串字符）

### 3. 在 R 中设置 API Key
```r
# 方法1: 设置环境变量（推荐）
Sys.setenv(CHEMSPIDER_API_KEY = "your_api_key_here")

# 方法2: 直接传入函数参数
api_key <- "your_api_key_here"
```

## 🔧 API 端点信息

**重要提示**: 由于 ChemSpider API 的变化，确切的端点 URL 可能需要根据官方文档调整。

推测的新 API 端点：
- 基础 URL: `https://api.rsc.org/compounds`
- InChIKey 查询: `https://api.rsc.org/compounds/filter/inchikey/{inchikey}`
- 名称查询: `https://api.rsc.org/compounds/filter/name/{name}`

## 📝 使用示例

### 1. 基本设置
```r
# 加载 ChemSpider 集成脚本
source("chemspider_integration.R")

# 查看设置指南
setup_chemspider_api()

# 设置 API key
Sys.setenv(CHEMSPIDER_API_KEY = "your_actual_api_key")

# 测试连接
test_chemspider_connection()
```

### 2. 通过 InChIKey 查询
```r
# 查询咖啡因
caffeine_inchikey <- "RYYVLZVUVIJVGH-UHFFFAOYSA-N"
result <- get_compound_from_chemspider_by_inchikey(caffeine_inchikey)

print(result)
```

### 3. 通过化合物名称查询
```r
# 查询阿司匹林
result <- get_compound_from_chemspider_by_name("aspirin")
print(result)
```

## ⚠️ 重要注意事项

### 1. API 端点可能需要调整
我提供的代码中的 API 端点是基于推测的，因为：
- ChemSpider 的新 API 文档可能不完全公开
- 实际的端点 URL 可能与推测的不同
- 请求格式和响应格式可能需要调整

### 2. 需要验证实际 API 格式
在使用前，您需要：
1. 登录 https://developer.rsc.org/ 查看实际的 API 文档
2. 确认正确的端点 URL
3. 了解请求头和参数格式
4. 确认响应数据的结构

### 3. 替代方案
如果 ChemSpider API 难以使用，建议：
1. **优先使用 PubChem**（免费且稳定）
2. **使用 NCI/CADD Chemical Identifier Resolver**（免费）
3. **考虑其他化学数据库 API**

## 🔄 集成到现有代码

要将 ChemSpider 集成到您现有的 `test.R` 文件中：

```r
# 在 test.R 中添加 ChemSpider 支持
source("chemspider_integration.R")

# 修改综合查询函数
get_compound_info_comprehensive <- function(compound_name = NULL, inchikey = NULL, 
                                          use_chemspider = TRUE, api_key = NULL) {
  # 首先尝试 PubChem
  pubchem_result <- get_compound_info_from_pubchem(compound_name)
  
  if (!all(is.na(pubchem_result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
    return(pubchem_result)
  }
  
  # 如果 PubChem 失败且启用 ChemSpider
  if (use_chemspider && check_chemspider_api_key(api_key)) {
    if (!is.null(compound_name)) {
      chemspider_result <- get_compound_from_chemspider_by_name(compound_name, api_key)
      if (!all(is.na(chemspider_result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
        return(chemspider_result)
      }
    }
    
    if (!is.null(inchikey)) {
      chemspider_result <- get_compound_from_chemspider_by_inchikey(inchikey, api_key)
      if (!all(is.na(chemspider_result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
        return(chemspider_result)
      }
    }
  }
  
  # 所有查询都失败
  return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, 
              InChIKey = NA, source = "All failed"))
}
```

## 🎯 下一步行动

1. **注册 RSC 开发者账户**并获取 API key
2. **测试实际的 API 端点**并调整代码
3. **验证响应格式**并更新解析逻辑
4. **集成到您的主要查询流程**中

## 📞 获取帮助

如果遇到问题：
1. 查看 RSC 开发者门户的官方文档
2. 联系 RSC 技术支持
3. 考虑使用其他化学数据库作为备选方案

---

**免责声明**: 由于 ChemSpider API 的变化，本指南中的某些技术细节可能需要根据官方最新文档进行调整。
