#####################################################
#                  正离子峰识别和匹配                   #
#             *使用时更改参数设置部分路径*               #
#####################################################
POS_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/POS","C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/result")
POS_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/POS","C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/result")
POS_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/POS","C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/result")
source("C:/Users/<USER>/Desktop/R/main.R")
POS_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/POS","C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/result")
##111.正离子匹配对齐，识别同位素
library(xcms)
filepath<-"C:/AAA-HUYU/Laboratory/R_project/DYL/20250717_Plasma_10ul/POS"
mzfiles<-list.files(filepath, recursive=TRUE, full.names=TRUE)
mzfiles
st<-Sys.time(); st
xset<-xcmsSet(mzfiles,method="AMW_SMI",
ppm=15,
peakwidth=c(3,10),
prefilter=c(8,100),
mzCenterFun="wMean",
integrate=1, mzdiff=-0.001,
fitgauss=TRUE,  noise=100,
sleep=0,
verboseColumns=TRUE,
#ROI.list=list(), firstBaselineCheck=TRUE, roiScales=NULL,scanrange= c(135,2392), wCoefsthresh=200,
ROI.list=list(), firstBaselineCheck=TRUE, roiScales=NULL,scanrange= numeric(), wCoefsthresh=200,
peakIntensity=500,rLLthresh=2,rLWthresh=8,vLLthresh=2,vLWthresh=8,peakHeight=500,eSNRthresh=6,SMIthresh=0.4,
muthreshp=5,hthreshp=0.3,sigmathreshp=2,pblthreshp=0.3,rSMIthresh=0.35,gsnthresh=6,tfwhm=c(1.5,25))
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/SIM.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
View(sim_df)
View(medicine_df)
sim_df
View(sim_df)
View(sim_df)
sim_df<- unique(sim_df$extracted_mz)
sim_df
mz_match <- function(medicine_file, sim_file, output, diff) {
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/SIM.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/SIM.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
View(medicine_df)
View(medicine_df)
View(sim_df)
View(medicine_df)
View(sim_df)
View(sim_df)
View(sim_df)
library(dplyr)
sim_df <- sim_df %>%
group_by(`PC group`) %>%
slice(1) %>%
ungroup()
View(medicine_df)
View(sim_df)
sim_df <- sim_df %>%
group_by(`PC_Group`) %>%
slice(1) %>%
ungroup()
View(sim_df)
i = 1
med_mz <- medicine_df[i, "mz"]
med_mz
View(medicine_df)
mz_diff <- abs(sim_df$extracted_mz - med_mz)
matching_indices <- which(mz_diff <= diff)
mz_diff
matching_indices
View(medicine_df)
View(sim_df)
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/IDEN.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
library(dplyr)
sim_df <- sim_df %>%
group_by(`PC_Group`) %>%
slice(1) %>%
ungroup()
i = 363
med_mz <- medicine_df[i, "mz"]
mz_diff <- abs(sim_df$extracted_mz - med_mz)
matching_indices <- which(mz_diff <= diff)
med_mz
mz_diff
matching_indices <- which(mz_diff <= diff)
matching_indices
View(medicine_df)
for (j in matching_indices) {
matched_record <- data.frame(
Medicine_ID = medicine_df[i, "X"],
Medicine_mz = med_mz,
Medicine_RT = medicine_df[i, "rt"],
SIM_mz = sim_df[j, "extracted_mz"],
SIM_Name = sim_df[j, "NAME"],
SIM_InChIKey = sim_df[j, "INCHIKEY"],
SIM_PC_Group = sim_df[j, "PC_Group"],
mz_Difference = abs(sim_df[j, "extracted_mz"] - med_mz),
stringsAsFactors = FALSE
)
matched_records <- rbind(matched_records, matched_record)
}
# 存储匹配结果
matched_records <- data.frame()
for (j in matching_indices) {
matched_record <- data.frame(
Medicine_ID = medicine_df[i, "X"],
Medicine_mz = med_mz,
Medicine_RT = medicine_df[i, "rt"],
SIM_mz = sim_df[j, "extracted_mz"],
SIM_Name = sim_df[j, "NAME"],
SIM_InChIKey = sim_df[j, "INCHIKEY"],
SIM_PC_Group = sim_df[j, "PC_Group"],
mz_Difference = abs(sim_df[j, "extracted_mz"] - med_mz),
stringsAsFactors = FALSE
)
matched_records <- rbind(matched_records, matched_record)
}
matched_records
matched_records <- matched_records[order(matched_records$mz_Difference), ]
nrow(matched_records) > 0
matched_records <- matched_records[order(matched_records$mz_Difference), ]
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/IDEN.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
library(dplyr)
sim_df <- sim_df %>%
group_by(`PC_Group`) %>%
slice(1) %>%
ungroup()
# 存储匹配结果
matched_records <- data.frame()
# 匹配
for (i in 1:nrow(medicine_df)) {
# i = 363
med_mz <- medicine_df[i, "mz"]
mz_diff <- abs(sim_df$extracted_mz - med_mz)
matching_indices <- which(mz_diff <= diff)
if (length(matching_indices) > 0) {
for (j in matching_indices) {
matched_record <- data.frame(
Medicine_ID = medicine_df[i, "X"],
Medicine_mz = med_mz,
Medicine_RT = medicine_df[i, "rt"],
SIM_mz = sim_df[j, "extracted_mz"],
SIM_Name = sim_df[j, "NAME"],
SIM_InChIKey = sim_df[j, "INCHIKEY"],
SIM_PC_Group = sim_df[j, "PC_Group"],
mz_Difference = abs(sim_df[j, "extracted_mz"] - med_mz),
stringsAsFactors = FALSE
)
matched_records <- rbind(matched_records, matched_record)
}
}
}
View(matched_records)
# 保存结果
write.csv(matched_records, file.path(paste0(output,"/mz_match_0721.csv")), row.names = FALSE)
medicine_file <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv"
sim_file <-  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/IDEN.csv"
output <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/1"
diff <- 0.005
# 从File2列中提取mz值的内部函数
extract_mz <- function(file2_str) {
pattern <- "nM([0-9]+\\.?[0-9]*)"
matches <- regmatches(file2_str, regexpr(pattern, file2_str))
if (length(matches) > 0 && matches != "") {
mz_pattern <- "([0-9]+\\.?[0-9]*)"
mz_matches <- regmatches(matches, regexpr(mz_pattern, matches))
if (length(mz_matches) > 0) {
return(as.numeric(mz_matches))
}
}
return(NA)
}
# 读取输入文件
medicine_df <- read.csv(medicine_file, stringsAsFactors = FALSE)
sim_df <- read.csv(sim_file, stringsAsFactors = FALSE)
# 从SIM.csv的File2列中提取mz值
sim_df$extracted_mz <- sapply(sim_df$File2, extract_mz)
sim_df <- sim_df[!is.na(sim_df$extracted_mz), ]
# library(dplyr)
# sim_df <- sim_df %>%
#   group_by(`PC_Group`) %>%
#   slice(1) %>%
#   ungroup()
# 存储匹配结果
matched_records <- data.frame()
# 匹配
for (i in 1:nrow(medicine_df)) {
# i = 363
med_mz <- medicine_df[i, "mz"]
mz_diff <- abs(sim_df$extracted_mz - med_mz)
matching_indices <- which(mz_diff <= diff)
if (length(matching_indices) > 0) {
for (j in matching_indices) {
matched_record <- data.frame(
Medicine_ID = medicine_df[i, "X"],
Medicine_mz = med_mz,
Medicine_RT = medicine_df[i, "rt"],
SIM_mz = sim_df[j, "extracted_mz"],
SIM_Name = sim_df[j, "NAME"],
SIM_InChIKey = sim_df[j, "INCHIKEY"],
SIM_PC_Group = sim_df[j, "PC_Group"],
mz_Difference = abs(sim_df[j, "extracted_mz"] - med_mz),
stringsAsFactors = FALSE
)
matched_records <- rbind(matched_records, matched_record)
}
}
}
# 保存结果
write.csv(matched_records, file.path(paste0(output,"/mz_match_0721.csv")), row.names = FALSE)
View(matched_records)
# 保存结果
write.csv(matched_records, file.path(paste0(output,"/mz_match_0721_1.csv")), row.names = FALSE)
