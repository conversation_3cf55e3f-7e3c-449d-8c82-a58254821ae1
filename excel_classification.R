# 加载必要的库
library(readxl)
library(dplyr)
library(stringr)
library(parallel)
library(foreach)
library(doParallel)

classify_excel_files <- function(source_dir = NULL, output_base_dir = NULL, n_cores = NULL, mz_tolerance = 0.005, convert_to_txt = TRUE) {
  # 检查源目录是否存在
  if (!dir.exists(source_dir)) {
    stop("错误: 指定的源目录不存在: ", source_dir)
  }

  # 创建输出目录结构
  different_dir <- file.path(output_base_dir, "Different")
  sim_dir <- file.path(output_base_dir, "SIM")
  sim_mz_dir <- file.path(sim_dir, "SIM_MZ")
  different_mz_dir <- file.path(sim_dir, "Different_MZ")
  
  # 创建目录
  dir.create(different_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(sim_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(sim_mz_dir, recursive = TRUE, showWarnings = FALSE)
  dir.create(different_mz_dir, recursive = TRUE, showWarnings = FALSE)
  
  # 获取所有Excel文件
  excel_files <- list.files(source_dir, pattern = "\\.xlsx?$", full.names = TRUE, ignore.case = TRUE)
  
  if (length(excel_files) == 0) {
    stop("在指定目录中没有找到Excel文件")
  }
  
  cat("找到", length(excel_files), "个Excel文件\n")

  # 设置并行处理
  if (is.null(n_cores)) {
    n_cores <- min(detectCores() - 1, length(excel_files))  # 保留一个核心给系统
    n_cores <- max(1, n_cores)  # 至少使用1个核心
  }

  cat("使用", n_cores, "个CPU核心进行并行处理\n")

  # 注册并行后端
  cl <- makeCluster(n_cores)
  registerDoParallel(cl)

  # 确保在函数结束时关闭集群
  on.exit({
    stopCluster(cl)
    registerDoSEQ()  # 恢复到顺序处理
  })
  
  # 并行提取每个文件的信息
  cat("正在并行读取Excel文件信息...\n")

  # 使用foreach进行并行处理
  file_info_list <- foreach(file_path = excel_files,
                           .packages = c("readxl", "stringr"),
                           .export = c("extract_mother_ion"),
                           .errorhandling = "pass") %dopar% {

    file_name <- basename(file_path)

    tryCatch({
      # 读取第二个sheet的NAME列
      sheets <- excel_sheets(file_path)
      if (length(sheets) < 2) {
        return(list(error = paste("文件", file_name, "没有第二个sheet")))
      }

      # 读取第二个sheet的第一行第一列来获取NAME信息
      sheet2_data <- read_excel(file_path, sheet = 2, col_names = FALSE, n_max = 1)

      # 检查第一行第一列是否包含NAME信息
      if (nrow(sheet2_data) == 0 || ncol(sheet2_data) == 0) {
        return(list(error = paste("文件", file_name, "的第二个sheet为空")))
      }

      first_cell <- sheet2_data[1, 1]
      if (is.na(first_cell) || !grepl("NAME:", as.character(first_cell), ignore.case = TRUE)) {
        return(list(error = paste("文件", file_name, "的第二个sheet第一行第一列不包含NAME信息")))
      }

      # 提取NAME值（NAME:后面的内容）
      name_text <- as.character(first_cell)
      name_value <- sub(".*NAME:\\s*", "", name_text, ignore.case = TRUE)
      name_value <- trimws(name_value)  # 去除前后空格

      if (name_value == "" || is.na(name_value)) {
        return(list(error = paste("文件", file_name, "的NAME值为空")))
      }

      # 提取母离子（文件名中第二个和第三个下划线之间的数字）
      mother_ion <- extract_mother_ion(file_name)

      # 返回文件信息
      return(list(
        file_path = file_path,
        file_name = file_name,
        name_value = as.character(name_value),
        mother_ion = mother_ion,
        success = TRUE
      ))

    }, error = function(e) {
      return(list(error = paste("读取文件", file_name, "时出错:", e$message)))
    })
  }

  # 处理并行结果
  file_info <- data.frame(
    file_path = character(),
    file_name = character(),
    name_value = character(),
    mother_ion = character(),
    stringsAsFactors = FALSE
  )

  error_count <- 0
  for (result in file_info_list) {
    if (!is.null(result$error)) {
      warning(result$error)
      error_count <- error_count + 1
    } else if (!is.null(result$success)) {
      file_info <- rbind(file_info, data.frame(
        file_path = result$file_path,
        file_name = result$file_name,
        name_value = result$name_value,
        mother_ion = result$mother_ion,
        stringsAsFactors = FALSE
      ))
    }
  }
  
  cat("\n并行处理完成！\n")
  cat("成功读取", nrow(file_info), "个文件的信息\n")
  if (error_count > 0) {
    cat("跳过", error_count, "个有问题的文件\n")
  }
  
  # 开始分类
  cat("开始文件分类...\n")

  # 按NAME分组
  name_groups <- split(file_info, file_info$name_value)

  # 创建NAME到数字的映射
  name_mapping <- data.frame(
    number = integer(),
    name = character(),
    stringsAsFactors = FALSE
  )

  folder_counter <- 1

  for (name_group in names(name_groups)) {
    group_files <- name_groups[[name_group]]

    if (nrow(group_files) == 1) {
      # 只有一个文件，放入Different文件夹
      copy_file_to_dir(group_files$file_path[1], different_dir, convert_to_txt)
      cat("文件", group_files$file_name[1], "-> Different (唯一NAME)\n")
    } else {
      # 多个文件有相同的NAME，需要进一步按母离子分类

      # 在SIM_MZ中创建以数字命名的子文件夹
      numeric_folder_name <- as.character(folder_counter)
      name_dir <- file.path(sim_mz_dir, numeric_folder_name)
      dir.create(name_dir, recursive = TRUE, showWarnings = FALSE)

      # 记录映射关系
      name_mapping <- rbind(name_mapping, data.frame(
        number = folder_counter,
        name = name_group,
        stringsAsFactors = FALSE
      ))

      # 按母离子分组（考虑误差容忍度）
      mz_grouping_result <- group_mother_ions_by_tolerance(group_files, mz_tolerance)
      mz_groups <- mz_grouping_result$groups
      invalid_mz_files <- mz_grouping_result$invalid

      # 处理无效的母离子文件（放入Different_MZ）
      if (nrow(invalid_mz_files) > 0) {
        for (k in seq_len(nrow(invalid_mz_files))) {
          copy_file_to_dir(invalid_mz_files$file_path[k], different_mz_dir, convert_to_txt)
          cat("文件", invalid_mz_files$file_name[k], "-> Different_MZ (无效母离子)\n")
        }
      }

      # 处理有效的母离子分组
      for (i in seq_along(mz_groups)) {
        mz_files <- mz_groups[[i]]
        group_mz <- mz_files$group_mz[1]  # 使用组的代表性母离子值

        if (nrow(mz_files) == 1) {
          # 只有一个文件在这个母离子组中，放入Different_MZ
          copy_file_to_dir(mz_files$file_path[1], different_mz_dir, convert_to_txt)
          cat("文件", mz_files$file_name[1], "-> Different_MZ (唯一母离子组)\n")
        } else {
          # 多个文件在相同的母离子组中，在数字文件夹中创建母离子子文件夹
          mz_dir <- file.path(name_dir, paste0("MZ_", group_mz))
          dir.create(mz_dir, recursive = TRUE, showWarnings = FALSE)

          for (j in seq_len(nrow(mz_files))) {
            copy_file_to_dir(mz_files$file_path[j], mz_dir, convert_to_txt)
            cat("文件", mz_files$file_name[j], "-> SIM_MZ/", numeric_folder_name, "/MZ_", group_mz,
                " (原始MZ:", mz_files$mother_ion[j], ")\n")
          }
        }
      }

      folder_counter <- folder_counter + 1
    }
  }

  # 保存NAME映射文件
  if (nrow(name_mapping) > 0) {
    mapping_file <- file.path(sim_mz_dir, "folder_name_mapping.csv")
    write.csv(name_mapping, mapping_file, row.names = FALSE, fileEncoding = "UTF-8")
    cat("已创建文件夹名称映射文件:", mapping_file, "\n")
  }
  
  cat("\n文件分类完成！\n")
  cat("输出目录结构:\n")
  cat("- Different: NAME不同的文件\n")
  cat("- SIM/Different_MZ: NAME相同但母离子不同的文件\n")
  cat("- SIM/SIM_MZ/[NAME]/MZ_[母离子]: NAME和母离子都相同的文件\n")
}

# 辅助函数：提取母离子
extract_mother_ion <- function(filename) {
  # 移除文件扩展名
  name_without_ext <- tools::file_path_sans_ext(filename)

  # 按下划线分割
  parts <- strsplit(name_without_ext, "_")[[1]]

  if (length(parts) >= 3) {
    # 第二个下划线和第三个下划线之间的部分
    mother_ion_str <- parts[3]

    # 提取数字
    numbers <- str_extract_all(mother_ion_str, "\\d+\\.?\\d*")[[1]]
    if (length(numbers) > 0) {
      return(numbers[1])
    }
  }

  # 如果无法提取，返回"unknown"
  return("unknown")
}

# 辅助函数：根据误差容忍度对母离子进行分组
group_mother_ions_by_tolerance <- function(file_info, tolerance = 0.005) {
  # 转换母离子为数值，过滤掉无法转换的
  file_info$mz_numeric <- suppressWarnings(as.numeric(file_info$mother_ion))
  valid_files <- file_info[!is.na(file_info$mz_numeric), ]
  invalid_files <- file_info[is.na(file_info$mz_numeric), ]

  if (nrow(valid_files) == 0) {
    return(list(groups = list(), invalid = invalid_files))
  }

  # 按数值排序
  valid_files <- valid_files[order(valid_files$mz_numeric), ]

  # 分组算法
  groups <- list()
  group_counter <- 1

  for (i in seq_len(nrow(valid_files))) {
    current_mz <- valid_files$mz_numeric[i]
    assigned <- FALSE

    # 检查是否可以加入现有组
    for (j in seq_along(groups)) {
      group_mzs <- groups[[j]]$mz_numeric

      # 检查与组内任意一个母离子的差值是否在容忍度内
      if (any(abs(current_mz - group_mzs) <= tolerance)) {
        groups[[j]] <- rbind(groups[[j]], valid_files[i, ])
        assigned <- TRUE
        break
      }
    }

    # 如果没有合适的组，创建新组
    if (!assigned) {
      groups[[group_counter]] <- valid_files[i, ]
      group_counter <- group_counter + 1
    }
  }

  # 为每个组分配代表性的母离子值（使用组内平均值）
  for (i in seq_along(groups)) {
    avg_mz <- mean(groups[[i]]$mz_numeric)
    groups[[i]]$group_mz <- round(avg_mz, 3)
  }

  return(list(groups = groups, invalid = invalid_files))
}

# 辅助函数：清理文件名用作文件夹名
sanitize_filename <- function(name) {
  # 移除或替换不允许在文件夹名中使用的字符
  name <- gsub("[<>:\"/\\|?*]", "_", name)
  name <- gsub("\\s+", "_", name)  # 将空格替换为下划线
  return(name)
}

# 辅助函数：复制文件到指定目录（支持转换为TXT）
copy_file_to_dir <- function(source_file, dest_dir, convert_to_txt = FALSE) {
  if (!dir.exists(dest_dir)) {
    dir.create(dest_dir, recursive = TRUE, showWarnings = FALSE)
  }

  if (convert_to_txt) {
    # 转换为TXT格式
    return(convert_excel_to_txt(source_file, dest_dir))
  } else {
    # 直接复制Excel文件
    dest_file <- file.path(dest_dir, basename(source_file))

    # 如果目标文件已存在，添加数字后缀
    counter <- 1
    original_dest <- dest_file
    while (file.exists(dest_file)) {
      name_part <- tools::file_path_sans_ext(basename(original_dest))
      ext_part <- tools::file_ext(basename(original_dest))
      dest_file <- file.path(dest_dir, paste0(name_part, "_", counter, ".", ext_part))
      counter <- counter + 1
    }

    return(file.copy(source_file, dest_file))
  }
}

# 辅助函数：将Excel文件转换为TXT格式
convert_excel_to_txt <- function(source_file, dest_dir) {
  tryCatch({
    # 读取Excel文件的第一个sheet
    sheet1_data <- read_excel(source_file, sheet = 1)

    # 生成TXT文件名
    base_name <- tools::file_path_sans_ext(basename(source_file))
    txt_file <- file.path(dest_dir, paste0(base_name, ".txt"))

    # 如果目标文件已存在，添加数字后缀
    counter <- 1
    original_txt <- txt_file
    while (file.exists(txt_file)) {
      txt_file <- file.path(dest_dir, paste0(base_name, "_", counter, ".txt"))
      counter <- counter + 1
    }

    # 写入TXT文件（制表符分隔）
    write.table(sheet1_data, txt_file,
                sep = "\t",
                row.names = FALSE,
                col.names = TRUE,
                quote = FALSE,
                fileEncoding = "UTF-8",
                na = "")

    cat("  ✅ 转换为TXT:", basename(txt_file), "\n")
    return(TRUE)

  }, error = function(e) {
    cat("  ❌ 转换TXT失败:", basename(source_file), "-", e$message, "\n")
    return(FALSE)
  })
}