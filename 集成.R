# 引入函数脚本
source("HY_Function.R")

#设置参数
# #空白峰识别
# Blank_peak("C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/KB",
#            "C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/result")
#
# # #正离子峰识别和匹配
# POS_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/POS",
#               "C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/result")


# # #正离子同位素识别
# POS_isotope_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250725_M3_compare_resurvey_5ul/POS_correct",
#                  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250725_M3_compare_resurvey_5ul/result/POS_correct")

# #负离子峰识别和匹配
# NEG_peak_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250716_Plasma_10ul/sample/NEG","C:/AAA-HUYU/test")

# #负离子同位素识别
# NEG_isotope_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250716_Plasma_10ul/sample/NEG","C:/AAA-HUYU/test")

# #正负注释
# POS_NEG_Notes("C:/AAA-HUYU/Laboratory/R_project/DYL/20250604_DYL/data/sample/1h/POS",
#               "C:/AAA-HUYU/test",
#               "C:/AAA-HUYU/Laboratory/R_project/DYL/20250604_DYL/data/sample/1h/NEG",
#               "C:/AAA-HUYU/test",
#               "C:/AAA-HUYU/Laboratory/Annotation rules/zeqi/Zeqi_adducts_pos_neg_new.xlsx",
#               "C:/AAA-HUYU/test")

# #除去空白峰
# move_Blank("C:/AAA-HUYU/Laboratory/R_project/DYL/20250718/result/01pos_tIndex_data_all_each_sample.csv",
#             "C:/AAA-HUYU/Laboratory/R_project/DYL/20250718/result/POS_KB_peaks.csv",
#              "C:/AAA-HUYU/Laboratory/R_project/DYL/20250718/result",
#              0.005,
#              3)

#识别药物峰
# medicine("C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/ZQ_isolate.csv",
#         "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/None_isolate.csv",
#         "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match",
#         0.005,
#          3)

#合并分组，按照CSV文件名分组
# merge_csv_with_pc_groups("C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_Inchikey/02_MyData_excel_clean_result_20250718_IDEN",
#                          "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_Inchikey/result/ysid")

#添加puchem信息(Pro版本可以根据NAME或InCHIKey查询指定信息)
# Add_pubchem_info_pro("C:/AAA-HUYU/Laboratory/R_project/DYL/20250721_InChIKey/Sim_111.xlsx",
#              "C:/AAA-HUYU/Laboratory/R_project/DYL/20250721_InChIKey/sim_result_111.xlsx",
#                         delay = 0.5,
#                  force_query_all = TRUE,
#                           fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey"))

#Add_pubchem_info()
# pubchem_info_help()

# mz_match("C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/medicine.csv",
#          "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match/IDEN.csv",
#           "C:/AAA-HUYU/Laboratory/R_project/DYL/20250720_isolate_match",
#           0.005)

# #合并同一张谱中来源于同一个母离子的二级碎片
# merge_fragments_by_precursor("C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/POS",
#                              "C:/AAA-HUYU/Laboratory/R_project/DYL/20250801/POS/SPL",
#                              5,
#                              0.005)

#将msp中的内容生成独立的excel
# msp_search_merge("C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/input",
#                  "C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/output")

#msp第二步处理，增加中性丢失和相对强度
# process_mspexcel_parallel("C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/output/1_BILELIB19/POS",
#                           "C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/output/1_BILELIB19/POS_output")

#合并文件夹中子文件夹中的所有文件到输出文件夹
# merge_folders("C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/NEG",
#               "G:/Laboratory/DataBase/collated_database/NEG_db")

#删除匹配到的文件
# delete_match_files("C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/output/1_BILELIB19/POS_output",
#                    ".*_0\\.xlsx$")


#根据生成的msp表格进行分类，输出TXT或者xlsx
classify_excel_files("test",
                     "test_output",
                     10,
                     0.005,
                     TRUE)
