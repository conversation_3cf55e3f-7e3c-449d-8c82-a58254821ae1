# ChemSpider API 集成 - 2024年版本
# 需要先在 https://developer.rsc.org/ 注册获取 API key

# ChemSpider API 配置和查询函数
setup_chemspider_api <- function() {
  cat("🔧 ChemSpider API 设置指南\n")
  cat("=" %R% 50, "\n")
  cat("1. 访问 https://developer.rsc.org/\n")
  cat("2. 注册账户或登录\n")
  cat("3. 获取您的 API key\n")
  cat("4. 将 API key 设置为环境变量或直接传入函数\n")
  cat("\n设置环境变量的方法:\n")
  cat('Sys.setenv(CHEMSPIDER_API_KEY = "your_api_key_here")\n')
  cat("=" %R% 50, "\n")
}

# 验证 API key 是否可用
check_chemspider_api_key <- function(api_key = NULL) {
  if (is.null(api_key)) {
    api_key <- Sys.getenv("CHEMSPIDER_API_KEY")
  }
  
  if (api_key == "" || is.na(api_key)) {
    cat("❌ 未找到 ChemSpider API key\n")
    cat("请先设置 API key:\n")
    setup_chemspider_api()
    return(FALSE)
  }
  
  cat("✅ 找到 ChemSpider API key\n")
  return(TRUE)
}

# ChemSpider 新版 API 查询函数 - 通过 InChIKey
get_compound_from_chemspider_by_inchikey <- function(inchikey, api_key = NULL, max_retries = 3, delay = 1, verbose = TRUE) {
  library(httr)
  library(jsonlite)
  
  if (!check_chemspider_api_key(api_key)) {
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "No API key"))
  }
  
  if (is.null(api_key)) {
    api_key <- Sys.getenv("CHEMSPIDER_API_KEY")
  }
  
  if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Empty InChIKey"))
  }
  
  inchikey <- trimws(inchikey)
  
  # 验证 InChIKey 格式
  if (!grepl("^[A-Z]{14}-[A-Z]{10}-[A-Z]$", inchikey)) {
    if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s\n", inchikey))
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Invalid format"))
  }
  
  # 新版 ChemSpider API 端点 (这些是推测的端点，需要根据实际 API 文档调整)
  base_url <- "https://api.rsc.org/compounds"
  
  # 方法1: 尝试通过 InChIKey 直接查询
  search_url <- paste0(base_url, "/filter/inchikey/", inchikey)
  
  if (verbose) cat(sprintf("🔗 ChemSpider 查询URL: %s\n", search_url))
  
  for (attempt in 1:max_retries) {
    response <- try(GET(
      search_url,
      add_headers(
        "apikey" = api_key,
        "Content-Type" = "application/json"
      ),
      timeout(30)
    ), silent = TRUE)
    
    if (inherits(response, "try-error")) {
      if (verbose) cat(sprintf("❌ ChemSpider 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
      Sys.sleep(delay * attempt)
      next
    }
    
    status <- status_code(response)
    if (verbose) cat(sprintf("📡 ChemSpider HTTP状态码: %d\n", status))
    
    if (status == 200) {
      content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
      if (inherits(content_json, "try-error")) {
        if (verbose) cat("❌ ChemSpider JSON解析错误\n")
        Sys.sleep(delay * attempt)
        next
      }
      
      # 根据实际 API 响应格式解析数据
      if (verbose) cat("✅ 成功从 ChemSpider 获取数据\n")
      
      # 这里需要根据实际的 API 响应格式来解析
      # 以下是示例解析逻辑，需要根据实际情况调整
      return(list(
        SMILES = content_json$smiles %||% NA,
        MolecularFormula = content_json$molecularFormula %||% NA,
        MolecularWeight = content_json$molecularWeight %||% NA,
        InChIKey = content_json$inchiKey %||% inchikey,
        source = "ChemSpider"
      ))
      
    } else if (status == 401) {
      if (verbose) cat("❌ ChemSpider API key 无效或已过期\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Invalid API key"))
    } else if (status == 404) {
      if (verbose) cat(sprintf("❌ ChemSpider 中未找到该 InChIKey: %s\n", inchikey))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Not found"))
    } else if (status == 429) {
      if (verbose) cat("⏳ ChemSpider API 请求频率限制，等待重试...\n")
      Sys.sleep(delay * attempt * 2)
    } else {
      if (verbose) cat(sprintf("❌ ChemSpider HTTP错误 %d (尝试 %d/%d): %s\n", status, attempt, max_retries, inchikey))
      Sys.sleep(delay * attempt)
    }
  }
  
  return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Max retries exceeded"))
}

# ChemSpider 查询函数 - 通过化合物名称
get_compound_from_chemspider_by_name <- function(compound_name, api_key = NULL, max_retries = 3, delay = 1, verbose = TRUE) {
  library(httr)
  library(jsonlite)
  
  if (!check_chemspider_api_key(api_key)) {
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "No API key"))
  }
  
  if (is.null(api_key)) {
    api_key <- Sys.getenv("CHEMSPIDER_API_KEY")
  }
  
  if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Empty compound name"))
  }
  
  compound_name <- trimws(compound_name)
  name_encoded <- URLencode(compound_name, reserved = TRUE)
  
  # 新版 ChemSpider API 端点
  base_url <- "https://api.rsc.org/compounds"
  search_url <- paste0(base_url, "/filter/name/", name_encoded)
  
  if (verbose) cat(sprintf("🔗 ChemSpider 名称查询URL: %s\n", search_url))
  
  for (attempt in 1:max_retries) {
    response <- try(GET(
      search_url,
      add_headers(
        "apikey" = api_key,
        "Content-Type" = "application/json"
      ),
      timeout(30)
    ), silent = TRUE)
    
    if (inherits(response, "try-error")) {
      if (verbose) cat(sprintf("❌ ChemSpider 名称查询网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
      Sys.sleep(delay * attempt)
      next
    }
    
    status <- status_code(response)
    if (verbose) cat(sprintf("📡 ChemSpider 名称查询状态码: %d\n", status))
    
    if (status == 200) {
      content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
      if (inherits(content_json, "try-error")) {
        if (verbose) cat("❌ ChemSpider 名称查询JSON解析错误\n")
        Sys.sleep(delay * attempt)
        next
      }
      
      if (verbose) cat("✅ 成功从 ChemSpider 通过名称获取数据\n")
      
      # 根据实际 API 响应格式解析数据
      return(list(
        SMILES = content_json$smiles %||% NA,
        MolecularFormula = content_json$molecularFormula %||% NA,
        MolecularWeight = content_json$molecularWeight %||% NA,
        InChIKey = content_json$inchiKey %||% NA,
        source = "ChemSpider"
      ))
      
    } else if (status == 401) {
      if (verbose) cat("❌ ChemSpider API key 无效\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Invalid API key"))
    } else if (status == 404) {
      if (verbose) cat(sprintf("❌ ChemSpider 中未找到化合物: %s\n", compound_name))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Not found"))
    } else if (status == 429) {
      if (verbose) cat("⏳ ChemSpider API 请求频率限制\n")
      Sys.sleep(delay * attempt * 2)
    } else {
      if (verbose) cat(sprintf("❌ ChemSpider 名称查询HTTP错误 %d: %s\n", status, compound_name))
      Sys.sleep(delay * attempt)
    }
  }
  
  return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "ChemSpider", error = "Max retries exceeded"))
}

# 测试 ChemSpider API 连接
test_chemspider_connection <- function(api_key = NULL) {
  cat("🧪 测试 ChemSpider API 连接\n")
  cat("=" %R% 50, "\n")
  
  if (!check_chemspider_api_key(api_key)) {
    return(FALSE)
  }
  
  # 测试一个已知的 InChIKey (咖啡因)
  test_inchikey <- "RYYVLZVUVIJVGH-UHFFFAOYSA-N"
  cat(sprintf("测试 InChIKey: %s\n", test_inchikey))
  
  result <- get_compound_from_chemspider_by_inchikey(test_inchikey, api_key, verbose = TRUE)
  
  if (!is.na(result$error)) {
    cat(sprintf("❌ 测试失败: %s\n", result$error))
    return(FALSE)
  } else {
    cat("✅ ChemSpider API 连接测试成功\n")
    return(TRUE)
  }
}

# 辅助函数
`%||%` <- function(a, b) if (!is.null(a) && !is.na(a) && nchar(a) > 0) a else b

# 使用说明
cat("📋 ChemSpider API 使用说明\n")
cat("=" %R% 50, "\n")
cat("1. 首先运行 setup_chemspider_api() 查看设置指南\n")
cat("2. 获取 API key 后设置环境变量\n")
cat("3. 运行 test_chemspider_connection() 测试连接\n")
cat("4. 使用查询函数获取化合物信息\n")
cat("\n示例用法:\n")
cat('Sys.setenv(CHEMSPIDER_API_KEY = "your_api_key")\n')
cat('result <- get_compound_from_chemspider_by_inchikey("RYYVLZVUVIJVGH-UHFFFAOYSA-N")\n')
cat("=" %R% 50, "\n")
