delete_match_files <- function(directory = NULL, pattern = NULL) {
  # 如果没有提供目录，询问用户
  if (is.null(directory)) {
    directory <- readline(prompt = "请输入目标目录路径: ")
  }

  # 检查目录是否存在
  if (!dir.exists(directory)) {
    stop("错误: 指定的目录不存在: ", directory)
  }

  # 如果没有提供正则表达式，询问用户
  if (is.null(pattern)) {
    cat("请输入正则表达式模式来匹配要删除的文件:\n")
    cat("示例:\n")
    cat("  - .*_0\\.xlsx$     (匹配以'_0.xlsx'结尾的文件)\n")
    cat("  - ^temp.*\\.txt$  (匹配以'temp'开头、'.txt'结尾的文件)\n")
    cat("  - .*\\.log$       (匹配所有.log文件)\n")
    pattern <- readline(prompt = "正则表达式: ")
  }

  # 验证正则表达式
  tryCatch({
    grep(pattern, "test", perl = TRUE)
  }, error = function(e) {
    stop("错误: 无效的正则表达式: ", pattern)
  })

  # 设置工作目录
  original_wd <- getwd()
  on.exit(setwd(original_wd))  # 确保函数结束时恢复原目录
  setwd(directory)

  # 获取匹配的文件
  files_to_delete <- list.files(pattern = pattern, full.names = TRUE)

  # 检查是否有匹配的文件
  if (length(files_to_delete) == 0) {
    message("没有找到匹配模式 '", pattern, "' 的文件")
    return(invisible(NULL))
  }

  # 显示将要删除的文件
  message("在目录 '", directory, "' 中找到 ", length(files_to_delete), " 个匹配的文件:")
  cat("\n")
  for (i in seq_along(files_to_delete)) {
    cat(sprintf("%d. %s\n", i, basename(files_to_delete[i])))
  }
  cat("\n")

  # 确认是否继续删除
  user_input <- readline(prompt = "确认删除这些文件吗？(y/n): ")

  if (tolower(user_input) %in% c("y", "yes", "是")) {
    # 删除文件
    success_count <- 0
    failed_files <- character(0)

    for (file in files_to_delete) {
      if (file.remove(file)) {
        success_count <- success_count + 1
      } else {
        failed_files <- c(failed_files, file)
      }
    }

    # 报告结果
    if (success_count > 0) {
      message(sprintf("已成功删除 %d 个文件", success_count))
    }

    if (length(failed_files) > 0) {
      warning("以下文件删除失败:")
      for (file in failed_files) {
        cat("  - ", basename(file), "\n")
      }
    }
  } else {
    message("操作已取消")
  }
}
