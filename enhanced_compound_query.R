# 增强版化合物信息查询 - 使用多个可靠数据源
# 主要使用 PubChem，并添加其他可靠的备选方案

Add_compound_info_enhanced <- function(file_path, output_path = NULL,
                                       delay = 0.5,
                                       force_query_all = FALSE,
                                       fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey"),
                                       use_alternative_sources = TRUE) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  # PubChem 通过名称查询
  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem"))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ PubChem 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ PubChem JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem"))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          source = "PubChem"
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Not found"))
      } else {
        cat(sprintf("❌ PubChem HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Max retries exceeded"))
  }

  # PubChem 通过 InChIKey 查询
  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = FALSE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Empty InChIKey"))
    }

    inchikey <- trimws(inchikey)
    
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s\n", inchikey))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Invalid format"))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        if (verbose) cat(sprintf("❌ PubChem InChIKey 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Empty properties"))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          source = "PubChem"
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Not found"))
      } else {
        if (verbose) cat(sprintf("❌ PubChem InChIKey HTTP错误 %d: %s\n", status_code(response), inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "PubChem", error = "Max retries exceeded"))
  }

  # NIH NCI/CADD 数据库查询 (备选方案)
  get_compound_info_from_nci <- function(compound_name, max_retries = 2, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "NCI", error = "Empty name"))
    }

    # NCI/CADD Chemical Identifier Resolver
    name_encoded <- URLencode(compound_name, reserved = TRUE)
    
    # 尝试获取 SMILES
    smiles_url <- paste0("https://cactus.nci.nih.gov/chemical/structure/", name_encoded, "/smiles")
    
    for (attempt in 1:max_retries) {
      smiles_response <- try(GET(smiles_url, timeout(15)), silent = TRUE)
      if (!inherits(smiles_response, "try-error") && status_code(smiles_response) == 200) {
        smiles <- trimws(rawToChar(smiles_response$content))
        if (nchar(smiles) > 0 && !grepl("Error|not found", smiles, ignore.case = TRUE)) {
          return(list(
            SMILES = smiles,
            MolecularFormula = NA,
            MolecularWeight = NA,
            InChIKey = NA,
            source = "NCI"
          ))
        }
      }
      Sys.sleep(delay)
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "NCI", error = "Not found"))
  }

  # 综合查询函数
  get_compound_info_comprehensive <- function(compound_name = NULL, inchikey = NULL, max_retries = 3, delay = 1) {
    # 首先尝试 PubChem 名称查询
    if (!is.null(compound_name) && !is.na(compound_name) && nchar(trimws(compound_name)) > 0) {
      pubchem_result <- get_compound_info_from_pubchem(compound_name, max_retries, delay)
      
      if (!all(is.na(pubchem_result[c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")]))) {
        return(pubchem_result)
      }
      
      # PubChem 名称查询失败，尝试备选数据库
      if (use_alternative_sources) {
        nci_result <- get_compound_info_from_nci(compound_name, max_retries = 2, delay)
        if (!is.na(nci_result$SMILES)) {
          return(nci_result)
        }
      }
    }
    
    # 如果有 InChIKey，尝试 PubChem InChIKey 查询
    if (!is.null(inchikey) && !is.na(inchikey) && nchar(trimws(inchikey)) > 0) {
      pubchem_inchi_result <- get_compound_info_by_inchikey(inchikey, max_retries, delay)
      
      if (!all(is.na(pubchem_inchi_result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
        return(pubchem_inchi_result)
      }
    }
    
    # 所有查询都失败
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, source = "Multiple", error = "All sources failed"))
  }

  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # 主要查询逻辑
  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }
    
    # 添加数据来源列
    if (!"source" %in% colnames(df)) df$source <- NA

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      existing_inchikey <- if (!is.na(df$InChIKey[i]) && df$InChIKey[i] != "") df$InChIKey[i] else NULL
      result <- get_compound_info_comprehensive(
        compound_name = compound_name, 
        inchikey = existing_inchikey, 
        delay = delay
      )

      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }
      
      # 记录数据来源
      if (!is.na(result$source)) {
        df$source[i] <- result$source
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        source_info <- if (!is.null(result$source)) paste0(" (来源: ", result$source, ")") else ""
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), source_info, "\n")
      } else {
        cat("❌ 查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    return(df)
  }

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCG\\d+!", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)
  df <- df[!str_detect(df$NAME, "^Unknown"), ]

  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  if (use_alternative_sources) {
    cat("🔍 开始查询多个化学数据库 (PubChem + NCI)...\n")
  } else {
    cat("🔍 开始查询 PubChem 数据库...\n")
  }
  
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_enhanced_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)
  
  # 统计各数据源的贡献
  if ("source" %in% colnames(df_result)) {
    source_stats <- table(df_result$source[!is.na(df_result$source)])
    cat("📊 数据来源统计:\n")
    for (source in names(source_stats)) {
      cat(sprintf("  %s: %d 条记录\n", source, source_stats[source]))
    }
  }

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}
