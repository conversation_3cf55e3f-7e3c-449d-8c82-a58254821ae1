Add_pubchem_info_pro <- function(file_path, output_path = NULL,
                             delay = 0.5,
                             force_query_all = FALSE,
                             fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    # 标准 InChIKey 格式: 14个字符-10个字符-1个字符 (总共27个字符，包含2个连字符)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      if (verbose) cat("⚠️ InChIKey 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty InChIKey"))
    }

    inchikey <- trimws(inchikey)

    # 验证 InChIKey 格式
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s (长度: %d)\n", inchikey, nchar(inchikey)))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Invalid format"))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    if (verbose) cat(sprintf("🔗 查询URL: %s\n", url))

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        error_msg <- attr(response, "condition")$message
        if (verbose) cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s - %s\n", attempt, max_retries, inchikey, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      status <- status_code(response)
      if (verbose) cat(sprintf("📡 HTTP状态码: %d\n", status))

      if (status == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          if (verbose) cat(sprintf("❌ JSON解析错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
          Sys.sleep(delay * attempt)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          if (verbose) cat("⚠️ 响应中没有找到 PropertyTable 或 Properties\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "No properties in response"))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) {
          if (verbose) cat("⚠️ Properties 表为空\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty properties"))
        }

        props <- as.list(props_df[1, ])
        if (verbose) cat("✅ 成功获取化合物信息\n")

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          error = NA
        ))
      } else if (status == 404) {
        if (verbose) cat(sprintf("❌ 在 PubChem 中未找到该 InChIKey: %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in PubChem"))
      } else if (status == 400) {
        if (verbose) cat(sprintf("❌ 请求格式错误 (400): %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Bad request"))
      } else if (status == 503) {
        if (verbose) cat(sprintf("⏳ 服务暂时不可用 (503), 等待重试 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt * 2)  # 服务不可用时等待更长时间
      } else {
        if (verbose) cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status, attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded"))
  }

  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      result <- get_compound_info_from_pubchem(compound_name, delay = delay)

      # 如果 NAME 查询失败，并且存在 InChIKey，尝试用 InChIKey 查
      if (all(is.na(result[fields_to_update]) | result[fields_to_update] == "") &&
          !is.na(df$InChIKey[i]) && df$InChIKey[i] != "") {
        cat("🔁 通过名称查询失败，尝试使用 InChIKey 再查...\n")
        result <- get_compound_info_by_inchikey(df$InChIKey[i], delay = delay)
      }

      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), "\n")
      } else {
        cat("❌ 查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    return(df)
  }

  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCG\\d+!", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)
  df <- df[!str_detect(df$NAME, "^Unknown"), ]



  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  cat("🔍 开始查询 PubChem 数据库...\n")
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}

# 测试单个 InChIKey 的函数
test_inchikey <- function(inchikey, verbose = TRUE) {
  cat("🧪 测试 InChIKey:", inchikey, "\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")

  # 加载必要的库
  library(httr)
  library(jsonlite)

  # 定义辅助函数
  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      if (verbose) cat("⚠️ InChIKey 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty InChIKey"))
    }

    inchikey <- trimws(inchikey)

    # 验证 InChIKey 格式
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s (长度: %d)\n", inchikey, nchar(inchikey)))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Invalid format"))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    if (verbose) cat(sprintf("🔗 查询URL: %s\n", url))

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        error_msg <- attr(response, "condition")$message
        if (verbose) cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s - %s\n", attempt, max_retries, inchikey, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      status <- status_code(response)
      if (verbose) cat(sprintf("📡 HTTP状态码: %d\n", status))

      if (status == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          if (verbose) cat(sprintf("❌ JSON解析错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
          Sys.sleep(delay * attempt)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          if (verbose) cat("⚠️ 响应中没有找到 PropertyTable 或 Properties\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "No properties in response"))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) {
          if (verbose) cat("⚠️ Properties 表为空\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty properties"))
        }

        props <- as.list(props_df[1, ])
        if (verbose) cat("✅ 成功获取化合物信息\n")

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          error = NA
        ))
      } else if (status == 404) {
        if (verbose) cat(sprintf("❌ 在 PubChem 中未找到该 InChIKey: %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in PubChem"))
      } else if (status == 400) {
        if (verbose) cat(sprintf("❌ 请求格式错误 (400): %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Bad request"))
      } else if (status == 503) {
        if (verbose) cat(sprintf("⏳ 服务暂时不可用 (503), 等待重试 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt * 2)
      } else {
        if (verbose) cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status, attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded"))
  }

  # 使用改进的函数测试
  result <- get_compound_info_by_inchikey(inchikey, verbose = verbose)

  cat("\n📊 测试结果:\n")
  for (name in names(result)) {
    if (name != "error") {
      cat(sprintf("  %s: %s\n", name, ifelse(is.na(result[[name]]), "NA", result[[name]])))
    }
  }

  if (!is.na(result$error)) {
    cat(sprintf("  错误信息: %s\n", result$error))
  }

  cat(paste(rep("=", 50), collapse = ""), "\n")
  return(result)
}

# 批量测试多个 InChIKey 的函数
test_multiple_inchikeys <- function(inchikeys, delay = 1) {
  cat("🧪 批量测试", length(inchikeys), "个 InChIKey\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  results <- list()
  success_count <- 0

  for (i in seq_along(inchikeys)) {
    cat(sprintf("\n[%d/%d] ", i, length(inchikeys)))
    result <- test_inchikey(inchikeys[i], verbose = FALSE)
    results[[i]] <- result

    if (!all(is.na(result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
      success_count <- success_count + 1
      cat("✅ 成功")
    } else {
      cat("❌ 失败:", result$error %||% "未知错误")
    }

    if (i < length(inchikeys)) Sys.sleep(delay)
  }

  cat(sprintf("\n\n📊 总结: %d/%d 成功 (%.1f%%)\n",
              success_count, length(inchikeys),
              success_count/length(inchikeys)*100))

  return(results)
}

# 示例用法和常见问题的 InChIKey 测试
demo_inchikey_issues <- function() {
  cat("🔍 演示常见的 InChIKey 查询问题\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  # 测试不同类型的 InChIKey
  test_cases <- list(
    "正确格式 - 咖啡因" = "RYYVLZVUVIJVGH-UHFFFAOYSA-N",
    "正确格式 - 阿司匹林" = "BSYNRYMUTXBXSQ-UHFFFAOYSA-N",
    "格式错误 - 太短" = "RYYVLZVUVIJVGH-UHFFFAOYSA",
    "格式错误 - 太长" = "RYYVLZVUVIJVGH-UHFFFAOYSA-N-EXTRA",
    "格式错误 - 小写字母" = "ryyvlzvuvijvgh-uhfffaoysa-n",
    "格式错误 - 包含数字" = "RYYVLZVUVIJVGH-UHFFFAOYSA-1",
    "不存在的 InChIKey" = "AAAAAAAAAAAAAA-AAAAAAAAAA-A",
    "空字符串" = "",
    "只有空格" = "   "
  )

  for (name in names(test_cases)) {
    cat(sprintf("\n🧪 测试: %s\n", name))
    cat(sprintf("InChIKey: '%s'\n", test_cases[[name]]))
    result <- test_inchikey(test_cases[[name]], verbose = FALSE)

    if (is.na(result$error)) {
      cat("✅ 查询成功\n")
    } else {
      cat(sprintf("❌ 失败原因: %s\n", result$error))
    }
    cat(paste(rep("-", 40), collapse = ""), "\n")
  }
}