Add_pubchem_info_pro <- function(file_path, output_path = NULL,
                                       delay = 0.5,
                                       force_query_all = TRUE,
                                       fields_to_update = c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey"),
                                       use_chemspider = TRUE) {

  library(httr)
  library(jsonlite)
  library(readxl)
  library(openxlsx)
  library(stringr)

  allowed_fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  fields_to_update <- intersect(fields_to_update, allowed_fields)
  if (length(fields_to_update) == 0) stop("❌ 请至少选择一个有效字段进行查询")

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_from_pubchem <- function(compound_name, max_retries = 3, delay = 1) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
    }

    name_encoded <- URLencode(compound_name, reserved = TRUE)
    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/name/", name_encoded,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        cat(sprintf("❌ 网络错误 (尝试 %d/%d): %s\n", attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
        next
      }

      if (status_code(response) == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          cat(sprintf("❌ JSON解析错误: %s\n", compound_name))
          Sys.sleep(delay * attempt)
          next
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
        props <- as.list(props_df[1, ])

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA
        ))
      } else if (status_code(response) == 404) {
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
      } else {
        cat(sprintf("❌ HTTP错误 %d (尝试 %d/%d): %s\n", status_code(response), attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA))
  }

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    # 标准 InChIKey 格式: 14个字符-10个字符-1个字符 (总共27个字符，包含2个连字符)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      if (verbose) cat("⚠️ InChIKey 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty InChIKey"))
    }

    inchikey <- trimws(inchikey)

    # 验证 InChIKey 格式
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s (长度: %d)\n", inchikey, nchar(inchikey)))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Invalid format"))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    if (verbose) cat(sprintf("🔗 查询URL: %s\n", url))

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        error_msg <- attr(response, "condition")$message
        if (verbose) cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s - %s\n", attempt, max_retries, inchikey, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      status <- status_code(response)
      if (verbose) cat(sprintf("📡 HTTP状态码: %d\n", status))

      if (status == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          if (verbose) cat(sprintf("❌ JSON解析错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
          Sys.sleep(delay * attempt)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          if (verbose) cat("⚠️ 响应中没有找到 PropertyTable 或 Properties\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "No properties in response"))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) {
          if (verbose) cat("⚠️ Properties 表为空\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty properties"))
        }

        props <- as.list(props_df[1, ])
        if (verbose) cat("✅ 成功获取化合物信息\n")

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          error = NA
        ))
      } else if (status == 404) {
        if (verbose) cat(sprintf("❌ 在 PubChem 中未找到该 InChIKey: %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in PubChem"))
      } else if (status == 400) {
        if (verbose) cat(sprintf("❌ 请求格式错误 (400): %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Bad request"))
      } else if (status == 503) {
        if (verbose) cat(sprintf("⏳ 服务暂时不可用 (503), 等待重试 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt * 2)  # 服务不可用时等待更长时间
      } else {
        if (verbose) cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status, attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded"))
  }

  # ChemSpider 查询函数 - 通过 InChIKey 查询
  get_compound_info_from_chemspider_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      if (verbose) cat("⚠️ InChIKey 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty InChIKey", source = "ChemSpider"))
    }

    inchikey <- trimws(inchikey)

    # 验证 InChIKey 格式
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s (长度: %d)\n", inchikey, nchar(inchikey)))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Invalid format", source = "ChemSpider"))
    }

    # ChemSpider 免费 API - 通过 InChIKey 搜索
    search_url <- paste0("https://www.chemspider.com/InChI.asmx/InChIKeyToCSID?inchi_key=", inchikey)

    if (verbose) cat(sprintf("🔗 ChemSpider 搜索URL: %s\n", search_url))

    for (attempt in 1:max_retries) {
      # 第一步：通过 InChIKey 获取 CSID
      search_response <- try(GET(search_url, timeout(30)), silent = TRUE)
      if (inherits(search_response, "try-error")) {
        error_msg <- attr(search_response, "condition")$message
        if (verbose) cat(sprintf("❌ ChemSpider 搜索网络错误 (尝试 %d/%d): %s - %s\n", attempt, max_retries, inchikey, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      search_status <- status_code(search_response)
      if (verbose) cat(sprintf("📡 ChemSpider 搜索状态码: %d\n", search_status))

      if (search_status == 200) {
        # 解析 XML 响应获取 CSID
        search_content <- rawToChar(search_response$content)

        # 简单的 XML 解析来提取 CSID
        csid_match <- regexpr("<int[^>]*>([0-9]+)</int>", search_content)
        if (csid_match[1] > 0) {
          csid <- regmatches(search_content, csid_match)
          csid <- gsub("<[^>]*>", "", csid)  # 移除 XML 标签

          if (verbose) cat(sprintf("✅ 找到 CSID: %s\n", csid))

          # 第二步：通过 CSID 获取化合物详细信息
          detail_url <- paste0("https://www.chemspider.com/MassSpecAPI.asmx/GetExtendedCompoundInfo?CSID=", csid, "&token=")

          detail_response <- try(GET(detail_url, timeout(30)), silent = TRUE)
          if (inherits(detail_response, "try-error")) {
            if (verbose) cat(sprintf("❌ ChemSpider 详情查询网络错误: %s\n", attr(detail_response, "condition")$message))
            Sys.sleep(delay * attempt)
            next
          }

          detail_status <- status_code(detail_response)
          if (detail_status == 200) {
            detail_content <- rawToChar(detail_response$content)

            # 解析化合物信息
            smiles <- extract_xml_value(detail_content, "SMILES")
            formula <- extract_xml_value(detail_content, "MolecularFormula")
            weight <- extract_xml_value(detail_content, "MolecularWeight")

            if (verbose) cat("✅ 成功从 ChemSpider 获取化合物信息\n")

            return(list(
              SMILES = if(nzchar(smiles)) smiles else NA,
              MolecularFormula = if(nzchar(formula)) formula else NA,
              MolecularWeight = if(nzchar(weight)) as.numeric(weight) else NA,
              InChIKey = inchikey,
              error = NA,
              source = "ChemSpider"
            ))
          } else {
            if (verbose) cat(sprintf("❌ ChemSpider 详情查询HTTP错误: %d\n", detail_status))
          }
        } else {
          if (verbose) cat(sprintf("❌ ChemSpider 中未找到该 InChIKey: %s\n", inchikey))
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in ChemSpider", source = "ChemSpider"))
        }
      } else if (search_status == 404) {
        if (verbose) cat(sprintf("❌ ChemSpider 中未找到该 InChIKey: %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in ChemSpider", source = "ChemSpider"))
      } else {
        if (verbose) cat(sprintf("❌ ChemSpider HTTP错误 %d (尝试 %d/%d): %s\n", search_status, attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded", source = "ChemSpider"))
  }

  # ChemSpider 查询函数 - 通过化合物名称查询
  get_compound_info_from_chemspider_by_name <- function(compound_name, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(compound_name) || nchar(trimws(compound_name)) == 0) {
      if (verbose) cat("⚠️ 化合物名称为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty compound name", source = "ChemSpider"))
    }

    compound_name <- trimws(compound_name)
    name_encoded <- URLencode(compound_name, reserved = TRUE)

    # ChemSpider 免费 API - 通过名称搜索
    search_url <- paste0("https://www.chemspider.com/Search.asmx/SimpleSearch?query=", name_encoded, "&token=")

    if (verbose) cat(sprintf("🔗 ChemSpider 名称搜索URL: %s\n", search_url))

    for (attempt in 1:max_retries) {
      # 第一步：通过名称获取 CSID 列表
      search_response <- try(GET(search_url, timeout(30)), silent = TRUE)
      if (inherits(search_response, "try-error")) {
        error_msg <- attr(search_response, "condition")$message
        if (verbose) cat(sprintf("❌ ChemSpider 名称搜索网络错误 (尝试 %d/%d): %s - %s\n", attempt, max_retries, compound_name, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      search_status <- status_code(search_response)
      if (verbose) cat(sprintf("📡 ChemSpider 名称搜索状态码: %d\n", search_status))

      if (search_status == 200) {
        search_content <- rawToChar(search_response$content)

        # 提取第一个 CSID
        csid_match <- regexpr("<int[^>]*>([0-9]+)</int>", search_content)
        if (csid_match[1] > 0) {
          csid <- regmatches(search_content, csid_match)
          csid <- gsub("<[^>]*>", "", csid)

          if (verbose) cat(sprintf("✅ 通过名称找到 CSID: %s\n", csid))

          # 使用 CSID 获取详细信息
          return(get_compound_info_from_chemspider_by_csid(csid, verbose = verbose))
        } else {
          if (verbose) cat(sprintf("❌ ChemSpider 中未找到化合物: %s\n", compound_name))
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in ChemSpider", source = "ChemSpider"))
        }
      } else {
        if (verbose) cat(sprintf("❌ ChemSpider 名称搜索HTTP错误 %d (尝试 %d/%d): %s\n", search_status, attempt, max_retries, compound_name))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded", source = "ChemSpider"))
  }

  # 辅助函数：从 XML 内容中提取指定标签的值
  extract_xml_value <- function(xml_content, tag_name) {
    pattern <- paste0("<", tag_name, "[^>]*>([^<]*)</", tag_name, ">")
    match <- regexpr(pattern, xml_content, ignore.case = TRUE)
    if (match[1] > 0) {
      full_match <- regmatches(xml_content, match)
      # 提取标签内的内容
      value_pattern <- paste0("<", tag_name, "[^>]*>([^<]*)</", tag_name, ">")
      value_match <- regexec(value_pattern, full_match, ignore.case = TRUE)
      if (length(value_match[[1]]) > 1) {
        return(regmatches(full_match, value_match)[[1]][2])
      }
    }
    return("")
  }

  # 通过 CSID 获取化合物详细信息
  get_compound_info_from_chemspider_by_csid <- function(csid, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(csid) || nchar(trimws(csid)) == 0) {
      if (verbose) cat("⚠️ CSID 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty CSID", source = "ChemSpider"))
    }

    csid <- trimws(csid)

    # ChemSpider 详细信息 API
    detail_url <- paste0("https://www.chemspider.com/MassSpecAPI.asmx/GetExtendedCompoundInfo?CSID=", csid, "&token=")

    if (verbose) cat(sprintf("🔗 ChemSpider 详情URL: %s\n", detail_url))

    for (attempt in 1:max_retries) {
      detail_response <- try(GET(detail_url, timeout(30)), silent = TRUE)
      if (inherits(detail_response, "try-error")) {
        error_msg <- attr(detail_response, "condition")$message
        if (verbose) cat(sprintf("❌ ChemSpider 详情查询网络错误 (尝试 %d/%d): %s - %s\n", attempt, max_retries, csid, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      detail_status <- status_code(detail_response)
      if (verbose) cat(sprintf("📡 ChemSpider 详情状态码: %d\n", detail_status))

      if (detail_status == 200) {
        detail_content <- rawToChar(detail_response$content)

        # 解析化合物信息
        smiles <- extract_xml_value(detail_content, "SMILES")
        formula <- extract_xml_value(detail_content, "MolecularFormula")
        weight <- extract_xml_value(detail_content, "MolecularWeight")
        inchikey <- extract_xml_value(detail_content, "InChIKey")

        if (verbose) cat("✅ 成功从 ChemSpider 获取化合物信息\n")

        return(list(
          SMILES = if(nzchar(smiles)) smiles else NA,
          MolecularFormula = if(nzchar(formula)) formula else NA,
          MolecularWeight = if(nzchar(weight)) as.numeric(weight) else NA,
          InChIKey = if(nzchar(inchikey)) inchikey else NA,
          error = NA,
          source = "ChemSpider"
        ))
      } else if (detail_status == 404) {
        if (verbose) cat(sprintf("❌ ChemSpider 中未找到 CSID: %s\n", csid))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "CSID not found in ChemSpider", source = "ChemSpider"))
      } else {
        if (verbose) cat(sprintf("❌ ChemSpider 详情HTTP错误 %d (尝试 %d/%d): %s\n", detail_status, attempt, max_retries, csid))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded", source = "ChemSpider"))
  }

  # 综合查询函数：先尝试 PubChem，再尝试 ChemSpider
  get_compound_info_comprehensive <- function(compound_name = NULL, inchikey = NULL, max_retries = 3, delay = 1, verbose = TRUE) {
    results <- list()

    # 如果提供了化合物名称，先尝试 PubChem
    if (!is.null(compound_name) && !is.na(compound_name) && nchar(trimws(compound_name)) > 0) {
      if (verbose) cat("🔍 尝试从 PubChem 通过名称查询...\n")
      pubchem_result <- get_compound_info_from_pubchem(compound_name, max_retries, delay)
      pubchem_result$source <- "PubChem"
      results$pubchem_name <- pubchem_result

      # 如果 PubChem 查询成功，返回结果
      if (!all(is.na(pubchem_result[c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")]))) {
        if (verbose) cat("✅ PubChem 名称查询成功\n")
        return(pubchem_result)
      }

      # PubChem 名称查询失败，尝试 ChemSpider
      if (verbose) cat("🔄 PubChem 名称查询失败，尝试 ChemSpider...\n")
      chemspider_result <- get_compound_info_from_chemspider_by_name(compound_name, max_retries, delay, verbose)
      results$chemspider_name <- chemspider_result

      if (!all(is.na(chemspider_result[c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")]))) {
        if (verbose) cat("✅ ChemSpider 名称查询成功\n")
        return(chemspider_result)
      }
    }

    # 如果提供了 InChIKey，尝试查询
    if (!is.null(inchikey) && !is.na(inchikey) && nchar(trimws(inchikey)) > 0) {
      if (verbose) cat("🔍 尝试从 PubChem 通过 InChIKey 查询...\n")
      pubchem_inchi_result <- get_compound_info_by_inchikey(inchikey, max_retries, delay, verbose)
      pubchem_inchi_result$source <- "PubChem"
      results$pubchem_inchikey <- pubchem_inchi_result

      # 如果 PubChem InChIKey 查询成功，返回结果
      if (!all(is.na(pubchem_inchi_result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
        if (verbose) cat("✅ PubChem InChIKey 查询成功\n")
        return(pubchem_inchi_result)
      }

      # PubChem InChIKey 查询失败，尝试 ChemSpider
      if (verbose) cat("🔄 PubChem InChIKey 查询失败，尝试 ChemSpider...\n")
      chemspider_inchi_result <- get_compound_info_from_chemspider_by_inchikey(inchikey, max_retries, delay, verbose)
      results$chemspider_inchikey <- chemspider_inchi_result

      if (!all(is.na(chemspider_inchi_result[c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")]))) {
        if (verbose) cat("✅ ChemSpider InChIKey 查询成功\n")
        return(chemspider_inchi_result)
      }
    }

    # 所有查询都失败
    if (verbose) cat("❌ 所有数据库查询都失败\n")
    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "All databases failed", source = "Multiple", results = results))
  }

  add_compound_info_to_df <- function(df, compound_col, delay = 1) {
    for (field in allowed_fields) {
      if (!(field %in% colnames(df))) df[[field]] <- NA
    }

    if (force_query_all) {
      need_query <- seq_len(nrow(df))
    } else {
      need_query <- which(
        is.na(df$InChIKey) | df$InChIKey == "" |
        (("SMILES" %in% fields_to_update) & (is.na(df$SMILES) | df$SMILES == ""))
      )
    }

    if (length(need_query) == 0) {
      cat("✅ 所有数据已完整，无需查询\n")
      return(df)
    }

    for (i in need_query) {
      compound_name <- df[[compound_col]][i]
      cat(sprintf("🔍 查询 (%d/%d): %s\n", which(need_query == i), length(need_query), compound_name))

      # 使用综合查询函数，自动尝试多个数据库
      existing_inchikey <- if (!is.na(df$InChIKey[i]) && df$InChIKey[i] != "") df$InChIKey[i] else NULL
      result <- get_compound_info_comprehensive(
        compound_name = compound_name,
        inchikey = existing_inchikey,
        delay = delay,
        verbose = FALSE
      )

      for (field in fields_to_update) {
        if (!is.na(result[[field]])) {
          df[[field]][i] <- result[[field]]
        }
      }

      found <- fields_to_update[!is.na(result[fields_to_update])]
      if (length(found) > 0) {
        source_info <- if (!is.null(result$source)) paste0(" (来源: ", result$source, ")") else ""
        cat("✅ 查询成功 - 获得:", paste(found, collapse = ", "), source_info, "\n")
      } else {
        cat("❌ 查询失败或结果为空\n")
      }

      Sys.sleep(delay)
    }

    return(df)
  }

  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # --- 主逻辑 ---
  cat("📖 读取文件:", file_path, "\n")
  df <- read_excel(file_path)
  if (!"NAME" %in% colnames(df)) stop("❌ 文件中未找到 'NAME' 列")
  cat(sprintf("📊 文件包含 %d 行数据\n", nrow(df)))

  cat("🧹 清洗化合物名称...\n")
  df$NAME <- str_replace(df$NAME, "^Massbank:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^ReSpect:[A-Za-z]{2}\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCGC\\d+-\\d+!", "")
  df$NAME <- str_replace(df$NAME, "^MoNA:\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "MassbankEU:SM\\d+\\s", "")
  df$NAME <- str_replace(df$NAME, "^NCG\\d+!", "")
  df$NAME <- str_extract(df$NAME, "[^|]+")
  df$NAME <- str_extract(df$NAME, "^[^;]+")
  df$NAME <- trimws(df$NAME)
  df <- df[!str_detect(df$NAME, "^Unknown"), ]



  sample_names <- head(df$NAME[!is.na(df$NAME) & df$NAME != ""], 3)
  if (length(sample_names) > 0) cat("📝 样本化合物名称:", paste(sample_names, collapse = ", "), "...\n")

  if (use_chemspider) {
    cat("🔍 开始查询多个化学数据库 (PubChem + ChemSpider)...\n")
  } else {
    cat("🔍 开始查询 PubChem 数据库...\n")
  }
  df_result <- add_compound_info_to_df(df, compound_col = "NAME", delay = delay)

  if (is.null(output_path)) {
    date_str <- format(Sys.Date(), "%Y%m%d")
    base_name <- tools::file_path_sans_ext(basename(file_path))
    output_path <- paste0(base_name, "_updated_", date_str, ".xlsx")
  }

  cat("💾 保存结果到:", output_path, "\n")
  write.xlsx(df_result, output_path)

  smiles_count <- sum(!is.na(df_result$SMILES) & df_result$SMILES != "", na.rm = TRUE)
  inchikey_count <- sum(!is.na(df_result$InChIKey) & df_result$InChIKey != "", na.rm = TRUE)

  cat("📦 查询完成！结果已保存到:", output_path, "\n")
  cat(sprintf("📈 统计结果: SMILES: %d/%d, InChIKey: %d/%d\n",
              smiles_count, nrow(df_result), inchikey_count, nrow(df_result)))
}

# 测试单个 InChIKey 的函数
test_inchikey <- function(inchikey, verbose = TRUE) {
  cat("🧪 测试 InChIKey:", inchikey, "\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")

  # 加载必要的库
  library(httr)
  library(jsonlite)

  # 定义辅助函数
  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  get_valid_smiles <- function(props) {
    if (is.list(props) || is.data.frame(props)) {
      smiles <- props$SMILES
      if (!is.null(smiles) && nzchar(smiles)) return(as.character(smiles))

      iso <- props$IsomericSMILES
      if (!is.null(iso) && nzchar(iso)) return(as.character(iso))

      cano <- props$CanonicalSMILES
      if (!is.null(cano) && nzchar(cano)) return(as.character(cano))

      conn <- props$ConnectivitySMILES
      if (!is.null(conn) && nzchar(conn)) return(as.character(conn))
    }
    return(NA)
  }

  get_compound_info_by_inchikey <- function(inchikey, max_retries = 3, delay = 1, verbose = TRUE) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) {
      if (verbose) cat("⚠️ InChIKey 为空或 NA\n")
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty InChIKey"))
    }

    inchikey <- trimws(inchikey)

    # 验证 InChIKey 格式
    if (!is_valid_inchikey(inchikey)) {
      if (verbose) cat(sprintf("⚠️ InChIKey 格式不正确: %s (长度: %d)\n", inchikey, nchar(inchikey)))
      return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Invalid format"))
    }

    url <- paste0("https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/inchikey/", inchikey,
                  "/property/IsomericSMILES,CanonicalSMILES,MolecularFormula,MolecularWeight,InChIKey/JSON")

    if (verbose) cat(sprintf("🔗 查询URL: %s\n", url))

    for (attempt in 1:max_retries) {
      response <- try(GET(url, timeout(30)), silent = TRUE)
      if (inherits(response, "try-error")) {
        error_msg <- attr(response, "condition")$message
        if (verbose) cat(sprintf("❌ 网络错误 (InChIKey 尝试 %d/%d): %s - %s\n", attempt, max_retries, inchikey, error_msg))
        Sys.sleep(delay * attempt)
        next
      }

      status <- status_code(response)
      if (verbose) cat(sprintf("📡 HTTP状态码: %d\n", status))

      if (status == 200) {
        content_json <- try(fromJSON(rawToChar(response$content)), silent = TRUE)
        if (inherits(content_json, "try-error")) {
          if (verbose) cat(sprintf("❌ JSON解析错误 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
          Sys.sleep(delay * attempt)
          next
        }

        if (is.null(content_json$PropertyTable) || is.null(content_json$PropertyTable$Properties)) {
          if (verbose) cat("⚠️ 响应中没有找到 PropertyTable 或 Properties\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "No properties in response"))
        }

        props_df <- content_json$PropertyTable$Properties
        if (nrow(props_df) == 0) {
          if (verbose) cat("⚠️ Properties 表为空\n")
          return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Empty properties"))
        }

        props <- as.list(props_df[1, ])
        if (verbose) cat("✅ 成功获取化合物信息\n")

        return(list(
          SMILES = get_valid_smiles(props),
          MolecularFormula = props$MolecularFormula %||% NA,
          MolecularWeight = props$MolecularWeight %||% NA,
          InChIKey = props$InChIKey %||% NA,
          error = NA
        ))
      } else if (status == 404) {
        if (verbose) cat(sprintf("❌ 在 PubChem 中未找到该 InChIKey: %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Not found in PubChem"))
      } else if (status == 400) {
        if (verbose) cat(sprintf("❌ 请求格式错误 (400): %s\n", inchikey))
        return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Bad request"))
      } else if (status == 503) {
        if (verbose) cat(sprintf("⏳ 服务暂时不可用 (503), 等待重试 (尝试 %d/%d): %s\n", attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt * 2)
      } else {
        if (verbose) cat(sprintf("❌ HTTP错误 %d (InChIKey 尝试 %d/%d): %s\n", status, attempt, max_retries, inchikey))
        Sys.sleep(delay * attempt)
      }
    }

    return(list(SMILES = NA, MolecularFormula = NA, MolecularWeight = NA, InChIKey = NA, error = "Max retries exceeded"))
  }

  # 使用改进的函数测试
  result <- get_compound_info_by_inchikey(inchikey, verbose = verbose)

  cat("\n📊 测试结果:\n")
  for (name in names(result)) {
    if (name != "error") {
      cat(sprintf("  %s: %s\n", name, ifelse(is.na(result[[name]]), "NA", result[[name]])))
    }
  }

  if (!is.na(result$error)) {
    cat(sprintf("  错误信息: %s\n", result$error))
  }

  cat(paste(rep("=", 50), collapse = ""), "\n")
  return(result)
}

# 批量测试多个 InChIKey 的函数
test_multiple_inchikeys <- function(inchikeys, delay = 1) {
  cat("🧪 批量测试", length(inchikeys), "个 InChIKey\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  results <- list()
  success_count <- 0

  for (i in seq_along(inchikeys)) {
    cat(sprintf("\n[%d/%d] ", i, length(inchikeys)))
    result <- test_inchikey(inchikeys[i], verbose = FALSE)
    results[[i]] <- result

    if (!all(is.na(result[c("SMILES", "MolecularFormula", "MolecularWeight")]))) {
      success_count <- success_count + 1
      cat("✅ 成功")
    } else {
      cat("❌ 失败:", result$error %||% "未知错误")
    }

    if (i < length(inchikeys)) Sys.sleep(delay)
  }

  cat(sprintf("\n\n📊 总结: %d/%d 成功 (%.1f%%)\n",
              success_count, length(inchikeys),
              success_count/length(inchikeys)*100))

  return(results)
}

# 示例用法和常见问题的 InChIKey 测试
demo_inchikey_issues <- function() {
  cat("🔍 演示常见的 InChIKey 查询问题\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  # 测试不同类型的 InChIKey
  test_cases <- list(
    "正确格式 - 咖啡因" = "RYYVLZVUVIJVGH-UHFFFAOYSA-N",
    "正确格式 - 阿司匹林" = "BSYNRYMUTXBXSQ-UHFFFAOYSA-N",
    "格式错误 - 太短" = "RYYVLZVUVIJVGH-UHFFFAOYSA",
    "格式错误 - 太长" = "RYYVLZVUVIJVGH-UHFFFAOYSA-N-EXTRA",
    "格式错误 - 小写字母" = "ryyvlzvuvijvgh-uhfffaoysa-n",
    "格式错误 - 包含数字" = "RYYVLZVUVIJVGH-UHFFFAOYSA-1",
    "不存在的 InChIKey" = "AAAAAAAAAAAAAA-AAAAAAAAAA-A",
    "空字符串" = "",
    "只有空格" = "   "
  )

  for (name in names(test_cases)) {
    cat(sprintf("\n🧪 测试: %s\n", name))
    cat(sprintf("InChIKey: '%s'\n", test_cases[[name]]))
    result <- test_inchikey(test_cases[[name]], verbose = FALSE)

    if (is.na(result$error)) {
      cat("✅ 查询成功\n")
    } else {
      cat(sprintf("❌ 失败原因: %s\n", result$error))
    }
    cat(paste(rep("-", 40), collapse = ""), "\n")
  }
}

# 测试 ChemSpider 查询功能
test_chemspider <- function(inchikey = NULL, compound_name = NULL, verbose = TRUE) {
  cat("🧪 测试 ChemSpider 查询功能\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")

  # 加载必要的库
  library(httr)
  library(jsonlite)

  # 定义辅助函数
  `%||%` <- function(a, b) if (!is.null(a)) a else b

  # 验证 InChIKey 格式的函数
  is_valid_inchikey <- function(inchikey) {
    if (is.na(inchikey) || nchar(trimws(inchikey)) == 0) return(FALSE)
    pattern <- "^[A-Z]{14}-[A-Z]{10}-[A-Z]$"
    return(grepl(pattern, trimws(inchikey)))
  }

  # 从 XML 内容中提取指定标签的值
  extract_xml_value <- function(xml_content, tag_name) {
    pattern <- paste0("<", tag_name, "[^>]*>([^<]*)</", tag_name, ">")
    match <- regexpr(pattern, xml_content, ignore.case = TRUE)
    if (match[1] > 0) {
      full_match <- regmatches(xml_content, match)
      value_pattern <- paste0("<", tag_name, "[^>]*>([^<]*)</", tag_name, ">")
      value_match <- regexec(value_pattern, full_match, ignore.case = TRUE)
      if (length(value_match[[1]]) > 1) {
        return(regmatches(full_match, value_match)[[1]][2])
      }
    }
    return("")
  }

  if (!is.null(inchikey)) {
    cat(sprintf("🔍 通过 InChIKey 测试: %s\n", inchikey))
    result <- get_compound_info_from_chemspider_by_inchikey(inchikey, verbose = verbose)
  } else if (!is.null(compound_name)) {
    cat(sprintf("🔍 通过化合物名称测试: %s\n", compound_name))
    result <- get_compound_info_from_chemspider_by_name(compound_name, verbose = verbose)
  } else {
    cat("❌ 请提供 InChIKey 或化合物名称\n")
    return(NULL)
  }

  cat("\n📊 ChemSpider 查询结果:\n")
  for (name in names(result)) {
    if (name != "error" && name != "source") {
      cat(sprintf("  %s: %s\n", name, ifelse(is.na(result[[name]]), "NA", result[[name]])))
    }
  }

  if (!is.na(result$error)) {
    cat(sprintf("  错误信息: %s\n", result$error))
  }

  cat(sprintf("  数据来源: %s\n", result$source %||% "未知"))
  cat(paste(rep("=", 50), collapse = ""), "\n")
  return(result)
}

# 比较 PubChem 和 ChemSpider 的查询结果
compare_databases <- function(inchikey = NULL, compound_name = NULL) {
  cat("🔍 比较 PubChem 和 ChemSpider 查询结果\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")

  if (!is.null(compound_name)) {
    cat(sprintf("化合物名称: %s\n", compound_name))
  }
  if (!is.null(inchikey)) {
    cat(sprintf("InChIKey: %s\n", inchikey))
  }
  cat("\n")

  # 测试 PubChem
  cat("📊 PubChem 查询结果:\n")
  cat(paste(rep("-", 30), collapse = ""), "\n")

  if (!is.null(compound_name)) {
    pubchem_result <- get_compound_info_from_pubchem(compound_name, max_retries = 2, delay = 0.5)
  } else if (!is.null(inchikey)) {
    pubchem_result <- get_compound_info_by_inchikey(inchikey, max_retries = 2, delay = 0.5, verbose = FALSE)
  }

  for (name in names(pubchem_result)) {
    if (name != "error") {
      cat(sprintf("  %s: %s\n", name, ifelse(is.na(pubchem_result[[name]]), "NA", pubchem_result[[name]])))
    }
  }

  # 测试 ChemSpider
  cat("\n📊 ChemSpider 查询结果:\n")
  cat(paste(rep("-", 30), collapse = ""), "\n")

  if (!is.null(compound_name)) {
    chemspider_result <- get_compound_info_from_chemspider_by_name(compound_name, max_retries = 2, delay = 0.5, verbose = FALSE)
  } else if (!is.null(inchikey)) {
    chemspider_result <- get_compound_info_from_chemspider_by_inchikey(inchikey, max_retries = 2, delay = 0.5, verbose = FALSE)
  }

  for (name in names(chemspider_result)) {
    if (name != "error" && name != "source") {
      cat(sprintf("  %s: %s\n", name, ifelse(is.na(chemspider_result[[name]]), "NA", chemspider_result[[name]])))
    }
  }

  # 比较结果
  cat("\n📈 结果比较:\n")
  cat(paste(rep("-", 30), collapse = ""), "\n")

  fields <- c("SMILES", "MolecularFormula", "MolecularWeight", "InChIKey")
  for (field in fields) {
    pub_val <- pubchem_result[[field]]
    chem_val <- chemspider_result[[field]]

    if (!is.na(pub_val) && !is.na(chem_val)) {
      if (pub_val == chem_val) {
        cat(sprintf("  %s: ✅ 一致\n", field))
      } else {
        cat(sprintf("  %s: ⚠️ 不一致 (PubChem: %s, ChemSpider: %s)\n", field, pub_val, chem_val))
      }
    } else if (!is.na(pub_val)) {
      cat(sprintf("  %s: 📊 仅 PubChem 有数据: %s\n", field, pub_val))
    } else if (!is.na(chem_val)) {
      cat(sprintf("  %s: 🕷️ 仅 ChemSpider 有数据: %s\n", field, chem_val))
    } else {
      cat(sprintf("  %s: ❌ 两个数据库都无数据\n", field))
    }
  }

  cat(paste(rep("=", 60), collapse = ""), "\n")
  return(list(pubchem = pubchem_result, chemspider = chemspider_result))
}